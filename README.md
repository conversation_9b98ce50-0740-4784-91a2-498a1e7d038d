# Ứng dụng Google Chat Bot

Ứng dụng này cho phép tích hợp bot vào Google Chat để gửi và nhận tin nhắn tự động.

## Tính năng

- ✅ Gửi tin nhắn đến Google Chat space
- ✅ Hỗ trợ cả Service Account và OAuth2 authentication
- ✅ Xử lý tin nhắn đến từ Google Chat
- ✅ Tích hợp với webhook để nhận tin nhắn real-time
- ✅ Hỗ trợ rich messages (cards, buttons, etc.)

## Cài đặt

### 1. Cài đặt dependencies

```bash
npm install
# hoặc
pnpm install
```

### 2. <PERSON><PERSON>u hình Google Cloud

#### Bước 1: Tạo Google Cloud Project
1. Vào [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project có sẵn
3. Ghi nhớ Project ID

#### Bước 2: Enable Google Chat API
1. Vào "APIs & Services" > "Library"
2. Tìm "Google Chat API" và click "Enable"

#### Bước 3: Tạo Service Account (Khuyến nghị)
1. Vào "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "Service Account"
3. Đặt tên: `chat-bot-service`
4. Click "Create and Continue" > "Done"
5. Trong danh sách Service Accounts, click vào service account vừa tạo
6. Vào tab "Keys" > "Add Key" > "Create new key" > "JSON"
7. Download file JSON về máy

#### Bước 4: Tạo OAuth2 Credentials (Tùy chọn)
1. Vào "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Chọn "Desktop application"
4. Đặt tên và tạo
5. Download file JSON

### 3. Cấu hình file credentials

1. Copy file Service Account JSON vào `src/data/service-account-key.json`
2. Copy file OAuth2 JSON vào `src/data/desktop-service-app.json`

### 4. Cấu hình Google Chat Space

1. Vào Google Chat space mà bạn muốn bot hoạt động
2. Click vào tên space > "Manage members"
3. Click "Add people and bots"
4. Thêm email của service account (có trong file JSON)
5. Cấp quyền "Member" hoặc "Admin"

### 5. Lấy Space ID

1. Vào Google Chat space
2. URL sẽ có dạng: `https://chat.google.com/room/AAQAqu0wnEU`
3. Space ID là phần cuối: `AAQAqu0wnEU`
4. Cập nhật trong code: `spaces/AAQAqu0wnEU`

## Sử dụng

### Chạy ứng dụng cơ bản

```bash
npm run google-chat
```

### Chạy ứng dụng với webhook

```bash
npm start
```

### Chạy trong chế độ development

```bash
npm run dev
```

## Cấu trúc dự án

```
├── src/
│   ├── data/                    # Thư mục chứa credentials
│   │   ├── service-account-key.json
│   │   └── desktop-service-app.json
│   ├── config/                  # Cấu hình ứng dụng
│   ├── google-chat.js          # Module Google Chat cơ bản
│   ├── google-chat-bot.js      # Bot với webhook support
│   └── index.js                # Entry point chính
├── package.json
└── README.md
```

## API Reference

### Gửi tin nhắn

```javascript
const { GoogleChatBot } = require('./src/google-chat-bot');

const bot = new GoogleChatBot();
await bot.sendMessage('spaces/YOUR_SPACE_ID', 'Xin chào!');
```

### Gửi rich message

```javascript
await bot.sendCard('spaces/YOUR_SPACE_ID', {
  header: {
    title: 'Thông báo',
    subtitle: 'Có tin nhắn mới'
  },
  sections: [{
    widgets: [{
      textParagraph: {
        text: 'Nội dung tin nhắn'
      }
    }]
  }],
  actionButtons: [{
    textButton: {
      text: 'Xem chi tiết',
      onClick: {
        action: {
          function: 'viewDetails'
        }
      }
    }
  }]
});
```

### Xử lý tin nhắn đến

```javascript
bot.onMessage((message) => {
  console.log('Nhận tin nhắn:', message);
  
  // Trả lời tin nhắn
  bot.reply(message, 'Cảm ơn bạn đã gửi tin nhắn!');
});
```

## Troubleshooting

### Lỗi "PERMISSION_DENIED"
- Kiểm tra Google Chat API đã enable chưa
- Kiểm tra service account đã được thêm vào space chưa
- Kiểm tra file service account key có đúng định dạng không

### Lỗi "Space not found"
- Kiểm tra Space ID có chính xác không
- Đảm bảo service account có quyền truy cập space đó

### Lỗi "Invalid credentials"
- Kiểm tra file credentials có đúng không
- Đảm bảo service account có quyền gửi tin nhắn

## Tích hợp với webhook

Để nhận tin nhắn real-time, bạn cần:

1. Deploy ứng dụng lên server có public URL
2. Cấu hình webhook URL trong Google Chat
3. Sử dụng `GoogleChatBot` class với webhook support

## Ví dụ sử dụng

Xem file `examples/` để có các ví dụ cụ thể về cách sử dụng bot.

## Đóng góp

Mọi đóng góp đều được chào đón! Vui lòng tạo issue hoặc pull request. 