{"name": "telegram-build-bot", "version": "1.0.0", "description": "Telegram bot for managing remote build processes", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "google-chat": "node src/google-chat.js", "google-chat-bot": "node src/google-chat-bot.js", "google-chat-dev": "nodemon src/google-chat-bot.js"}, "dependencies": {"@google-apps/chat": "^0.4.0", "@google-cloud/local-auth": "2.1.0", "@google-cloud/pubsub": "^4.5.0", "dotenv": "^16.3.1", "express": "^4.21.2", "google-auth-library": "^10.0.0", "googleapis": "^128.0.0", "node-telegram-bot-api": "^0.61.0", "ssh2": "^1.14.0"}, "devDependencies": {"nodemon": "^3.0.1"}}