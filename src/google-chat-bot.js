const express = require('express');
const GoogleChat = require('./google-chat');

class GoogleChatBot {
  constructor(options = {}) {
    this.googleChat = new GoogleChat();
    this.spaceId = options.spaceId || process.env.GOOGLE_CHAT_SPACE_ID || 'spaces/YOUR_SPACE_ID';
    this.port = options.port || process.env.PORT || 3000;
    this.app = express();
    this.messageHandlers = [];
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({ status: 'OK', timestamp: new Date().toISOString() });
    });

    // Webhook endpoint để nhận tin nhắn từ Google Chat
    this.app.post('/webhook', async (req, res) => {
      try {
        const event = req.body;
        console.log('📨 Received webhook event:', JSON.stringify(event, null, 2));

        // Xử lý các loại event khác nhau
        if (event.type === 'MESSAGE') {
          await this.handleMessage(event.message);
        } else if (event.type === 'ADDED_TO_SPACE') {
          await this.handleAddedToSpace(event);
        } else if (event.type === 'REMOVED_FROM_SPACE') {
          await this.handleRemovedFromSpace(event);
        }

        res.status(200).json({ status: 'OK' });
      } catch (error) {
        console.error('❌ Error processing webhook:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Endpoint để gửi tin nhắn thủ công
    this.app.post('/send-message', async (req, res) => {
      try {
        const { message, spaceId = this.spaceId } = req.body;
        
        if (!message) {
          return res.status(400).json({ error: 'Message is required' });
        }

        const result = await this.sendMessage(spaceId, message);
        res.json({ success: true, messageId: result.name });
      } catch (error) {
        console.error('❌ Error sending message:', error);
        res.status(500).json({ error: error.message });
      }
    });

    // Endpoint để gửi card
    this.app.post('/send-card', async (req, res) => {
      try {
        const { cardData, spaceId = this.spaceId } = req.body;
        
        if (!cardData) {
          return res.status(400).json({ error: 'Card data is required' });
        }

        const result = await this.sendCard(spaceId, cardData);
        res.json({ success: true, messageId: result.name });
      } catch (error) {
        console.error('❌ Error sending card:', error);
        res.status(500).json({ error: error.message });
      }
    });
  }

  // Đăng ký handler cho tin nhắn
  onMessage(handler) {
    this.messageHandlers.push(handler);
  }

  // Xử lý tin nhắn đến
  async handleMessage(message) {
    console.log('💬 Processing message:', message.text);
    
    // Chạy tất cả handlers đã đăng ký
    for (const handler of this.messageHandlers) {
      try {
        await handler(message);
      } catch (error) {
        console.error('❌ Error in message handler:', error);
      }
    }
  }

  // Xử lý khi bot được thêm vào space
  async handleAddedToSpace(event) {
    console.log('🎉 Bot added to space:', event.space.name);
    
    const welcomeMessage = `Xin chào! Tôi là bot assistant. Tôi có thể giúp bạn:
    
• Gửi tin nhắn: Chỉ cần nhắn tin bình thường
• Lệnh đặc biệt: 
  - /help - Xem hướng dẫn
  - /status - Kiểm tra trạng thái
  - /time - Xem thời gian hiện tại

Hãy thử nhắn tin với tôi!`;
    
    await this.sendMessage(event.space.name, welcomeMessage);
  }

  // Xử lý khi bot bị xóa khỏi space
  async handleRemovedFromSpace(event) {
    console.log('👋 Bot removed from space:', event.space.name);
  }

  // Gửi tin nhắn
  async sendMessage(spaceId, message) {
    return await this.googleChat.sendMessage(spaceId, message);
  }

  // Gửi card
  async sendCard(spaceId, cardData) {
    return await this.googleChat.sendCard(spaceId, cardData);
  }

  // Gửi rich message
  async sendRichMessage(spaceId, messageData) {
    return await this.googleChat.sendRichMessage(spaceId, messageData);
  }

  // Trả lời tin nhắn
  async reply(originalMessage, replyText) {
    const spaceId = originalMessage.space.name;
    return await this.sendMessage(spaceId, replyText);
  }

  // Trả lời với card
  async replyWithCard(originalMessage, cardData) {
    const spaceId = originalMessage.space.name;
    return await this.sendCard(spaceId, cardData);
  }

  // Khởi động server
  start() {
    return new Promise((resolve) => {
      this.server = this.app.listen(this.port, () => {
        console.log(`🚀 Google Chat Bot server running on port ${this.port}`);
        console.log(`📡 Webhook URL: http://localhost:${this.port}/webhook`);
        console.log(`💬 Send message: http://localhost:${this.port}/send-message`);
        console.log(`🃏 Send card: http://localhost:${this.port}/send-card`);
        resolve();
      });
    });
  }

  // Dừng server
  stop() {
    if (this.server) {
      this.server.close();
      console.log('🛑 Google Chat Bot server stopped');
    }
  }
}

module.exports = GoogleChatBot; 