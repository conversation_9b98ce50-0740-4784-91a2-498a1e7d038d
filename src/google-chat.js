const { google } = require('googleapis');
const path = require('path');
const fs = require('fs');

class GoogleChat {
  constructor() {
    this.auth = null;
    this.chat = null;
    this.spaceId = process.env.GOOGLE_CHAT_SPACE_ID || 'spaces/YOUR_SPACE_ID';
  }

  async initialize() {
    try {
      // Sử dụng Service Account
      const serviceAccountPath = path.join(__dirname, 'data', 'service-account-key.json');
      
      if (!fs.existsSync(serviceAccountPath)) {
        throw new Error('Service account key file not found. Please place your service account JSON file in src/data/service-account-key.json');
      }

      const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
      
      this.auth = new google.auth.GoogleAuth({
        credentials: serviceAccount,
        scopes: ['https://www.googleapis.com/auth/chat.messages']
      });

      this.chat = google.chat({ version: 'v1', auth: this.auth });
      
      console.log('✅ Google Chat initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Google Chat:', error.message);
      return false;
    }
  }

  async sendMessage(spaceId, message) {
    try {
      if (!this.chat) {
        await this.initialize();
      }

      const response = await this.chat.spaces.messages.create({
        parent: spaceId,
        requestBody: {
          text: message
        }
      });

      console.log('✅ Message sent successfully:', response.data.name);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to send message:', error.message);
      throw error;
    }
  }

  async sendCard(spaceId, cardData) {
    try {
      if (!this.chat) {
        await this.initialize();
      }

      const response = await this.chat.spaces.messages.create({
        parent: spaceId,
        requestBody: {
          cards: [cardData]
        }
      });

      console.log('✅ Card sent successfully:', response.data.name);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to send card:', error.message);
      throw error;
    }
  }

  async sendRichMessage(spaceId, messageData) {
    try {
      if (!this.chat) {
        await this.initialize();
      }

      const response = await this.chat.spaces.messages.create({
        parent: spaceId,
        requestBody: messageData
      });

      console.log('✅ Rich message sent successfully:', response.data.name);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to send rich message:', error.message);
      throw error;
    }
  }
}

module.exports = GoogleChat; 