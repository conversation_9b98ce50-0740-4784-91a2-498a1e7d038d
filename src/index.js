require('dotenv').config();
const TelegramBot = require('node-telegram-bot-api');
const { exec } = require('child_process');
const whitelistId = require('./config/whitelistid');
const projects = require('./config/projects');
const listRepo = require('./config/list-repo');
const {
  getListIteration,
  listNeedApprovalMemberMenu,
  getNeedApprovalTaskByMemberUsername,
  listNeedEstimateMemberMenu,
  getNeedEstimateTaskByMemberUsername,
  listApprovalMemberMenu,
  checkTokenExpired,
  approveTaskByMemberUsername,
  approveTaskByAllMembers
} = require('./om-bot');
const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, {
  polling: true
});
const fs = require('fs');
const path = require('path');

// <PERSON><PERSON><PERSON> nghĩa commands cho bot
const commands = [
  { command: 'start', description: 'Bắt đầu sử dụng bot' },
  { command: 'update_token', description: 'Cập nhật token mới' },
  { command: 'add_us_to_om', description: 'Thêm Story vào OM' },
  ...Object.entries(projects).map(([key, project]) => ({ command: project.commandChat, description: `Build ${project.name}` }))
];

// Set commands cho bot
bot.setMyCommands(commands);

// Thêm state để theo dõi người dùng đang cập nhật token
const userStates = {};

// Thêm command để bắt đầu cập nhật token
bot.onText(/\/update_token/, async (msg) => {
  const chatId = msg.chat.id;
  if (!await checkWhiteList(chatId)) {
    return;
  }
  
  userStates[chatId] = { waitingForToken: true };
  await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:');
});

const sendGoogleChatMessage = async (chat_web_hook_url = process.env.GOOGLE_CHAT_WEBHOOK_URL, message ) => {
  try {
    const response = await fetch(chat_web_hook_url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: message
      })
    });

    if (!response.ok) {
      // throw new Error(`HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('Error sending message to Google Chat:', error);
    throw error;
  }
}

// Hàm thực thi shell script
const executeDeploy = (projectName, chatId) => {
  return new Promise((resolve, reject) => {
    // Sử dụng đường dẫn tuyệt đối đến deploy.sh
    const deployScriptPath = `${__dirname}/deploy.sh`;
    console.log('Executing deploy script:', deployScriptPath);
    console.error(`sh ${deployScriptPath} ${projectName}`);
    
    exec(`sh ${deployScriptPath} ${projectName} ${chatId ?? ''}`, (error, stdout, stderr) => {
      if (error) {
        console.error('Deploy error:', error);
        reject(error);
        return;
      }
      resolve(stdout);
    });
  });
};

// Hàm thực thi shell script
const executeShell = (script, path) => {
  return new Promise((resolve, reject) => {
    exec(`cd ${path} && ${script}`, (error, stdout, stderr) => {
      if (error) {
        reject(error);
        return;
      }
      resolve(stdout);
    });
  });
};

// Xử lý lệnh /start
bot.onText(/\/start/, (msg) => {
  const chatId = msg.chat.id;
  const keyboard = {
    reply_markup: {
      inline_keyboard: [
        [{ text: '👤 List member', callback_data: 'list_member' }],
        [{ text: '🏗 Build Code', callback_data: 'build' }],
        [{ text: '🔍 List Repo', callback_data: 'list_repo' }],
        [{ text: '🚀 Active Iteration', callback_data: 'active_iteration' }],
        [{ text: '💡 List need approve task', callback_data: 'need_approve_task' }],
        [{ text: '⏰ List need estimate task', callback_data: 'need_estimate_task' }],
        [{ text: '✅ Approval task', callback_data: 'approval_task' }],
        [{ text: '🔍 Add Story to OM', callback_data: 'add_us_to_om' }],
        // ...Object.entries(projects).map(([key, project]) => ([
        //   { text: `${emojiList[Math.floor(Math.random() * emojiList.length)]} Build ${project.name}`, callback_data: `project_${key}` }
        // ]))
      ]
    }
  };

  bot.sendMessage(
    chatId,
    'Chào mừng đến với Build Bot!\nChọn một tác vụ bên dưới:',
    keyboard
  );
});

const buildMenu = async (chatId, messageId) => {
  // Hiển thị danh sách projects
  const projectKeyboard = {
    reply_markup: {
      inline_keyboard: [
        ...Object.entries(projects).map(([key, project]) => ([
          { text: project.name, callback_data: `project_${key}` }
        ]))
      ]
    }
  };
  
  await bot.editMessageText(
    'Chọn dự án để build:',
    {
      chat_id: chatId,
      message_id: messageId,
      ...projectKeyboard
    }
  );
}
const listRepoMenu = async (chatId, messageId) => {
  const listRepoKeyboard = {
    reply_markup: {
      inline_keyboard: [
        ...listRepo.map((repo) => ([
          { text: repo.name, callback_data: `repo_${repo.name}` }
        ]))
      ]
    }
  };
  
  await bot.editMessageText(
    'Chọn repo:',
    {
      chat_id: chatId,
      message_id: messageId,
      ...listRepoKeyboard
    }
  );
}

const buildJSProject = async (project, channelId, chatId, userName = '', isChannel = false) => {
  if (channelId) {
    await bot.sendMessage(channelId, `🏗 Đang build ${project.name} bởi ${isChannel ? 'CICD lỏ' : '@' + userName}...` );
  }
  if (project.googleChatWebhookUrl) {
    await sendGoogleChatMessage(project.googleChatWebhookUrl, `🏗 Đang build ${project.name} bởi ${isChannel ? 'CICD lỏ' : '@' + userName}...` );
  }
  await bot.sendMessage(chatId, `🏗 Đang build ${project.name} bởi ${isChannel ? 'CICD lỏ' : '@' + userName}...`);
  try {
    await executeShell(project.deployScript, project.path);
    if (channelId) {
      await bot.sendMessage(channelId, `✅ Build *${project.name}* thành công! Vui lòng truy cập vào ${project.domain} để kiểm tra.`, { parse_mode: 'Markdown' });
    }
    if (project.googleChatWebhookUrl) {
      await sendGoogleChatMessage(project.googleChatWebhookUrl, `✅ Build *${project.name}* thành công! Vui lòng truy cập vào ${project.domain} để kiểm tra.` );
    }
    await bot.sendMessage(chatId, `✅ Build *${project.name}* thành công! Vui lòng truy cập vào ${project.domain} để kiểm tra.`, { parse_mode: 'Markdown' });
  } catch (error) {
    await bot.sendMessage(chatId, `❌ Lỗi build *${project.name}*: ${error.message}`, { parse_mode: 'Markdown' });
    if (channelId) {
      await bot.sendMessage(channelId, `❌ Lỗi build *${project.name}*`, { parse_mode: 'Markdown' });
    }
    if (project.googleChatWebhookUrl) {
      await sendGoogleChatMessage(project.googleChatWebhookUrl, `❌ Lỗi build *${project.name}*`);
    }
  }
}

const buildPHPProject = async (project, channelId, chatId, userName = '', isChannel = false) => {
  if (channelId) {
    await bot.sendMessage(channelId, `🏗 Đang build ${project.name} bởi ${isChannel ? 'CICD lỏ' : '@' + userName}...`);
  }
  if (project.googleChatWebhookUrl) {
    await sendGoogleChatMessage(project.googleChatWebhookUrl, `🏗 Đang build ${project.name} bởi ${isChannel ? 'CICD lỏ' : '@' + userName}...` );
  }
  await bot.sendMessage(chatId, `🏗 Đang build ${project.name} bởi ${isChannel ? 'CICD lỏ' : '@' + userName}...`);
  try {
    await executeDeploy(project.name, project.chatId);
    if (channelId) {
      await bot.sendMessage(channelId, `✅ Build *${project.name}* thành công! Vui lòng truy cập vào ${project.domain} để kiểm tra.`, { parse_mode: 'Markdown' });
    }
    if (project.googleChatWebhookUrl) {
      await sendGoogleChatMessage(project.googleChatWebhookUrl, `✅ Build *${project.name}* thành công! Vui lòng truy cập vào ${project.domain} để kiểm tra.` );
    }
    await bot.sendMessage(chatId, `✅ Build *${project.name}* thành công! Vui lòng truy cập vào ${project.domain} để kiểm tra.`, { parse_mode: 'Markdown' });
  } catch (error) {
    if (channelId) {
      await bot.sendMessage(channelId, `❌ Lỗi build *${project.name}*`, { parse_mode: 'Markdown' });
    }
    if (project.googleChatWebhookUrl) {
      await sendGoogleChatMessage(project.googleChatWebhookUrl, `❌ Lỗi build *${project.name}*`);
    }
    await bot.sendMessage(chatId, `❌ Lỗi build *${project.name}*: ${error.message}`, { parse_mode: 'Markdown' });
  }
}

const checkWhiteList = async (chatId) => {
  if (chatId === -4518541881) {
    await bot.sendMessage(chatId, 'Vui lòng chat trực tiếp với bot.');

    return false;
  }
  if (!Object.values(whitelistId).some(user => user.id === chatId)) {
    await bot.sendMessage(chatId, 'Hãy về đội của dev 3 để sử dụng bot này.');
    return false;
  }
  return true;
}

const addUsToOm = async (chatId, messageId) => {
  
}
const chooseIteration = async (chatId, messageId) => {
  const listIteration = await getListIteration();
  const iterationKeyboard = {
    reply_markup: {
      inline_keyboard: [
        ...listIteration.map((iteration, index) => ([
          { text: iteration.name, callback_data: `iteration_${index}` }
        ]))
      ]
    }
  };
  console.log(messageId);
  // await bot.editMessageText(
  //   'Chọn iteration:',
  //   {
  //     chat_id: chatId,
  //     message_id: messageId,
  //     ...iterationKeyboard
  //   }
  // );
}
bot.onText(/\/add_us_to_om/, async (msg) => {
  const chatId = msg.chat.id;
  if (!await checkTokenExpired(bot, chatId)) {
    userStates[chatId] = { waitingForToken: true };
    await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:'); 
    return;
  }
  await chooseIteration(msg.chat.id, msg.message_id);
});

bot.on('callback_query', async (query) => {
  const chatId = query.message.chat.id;
  const userName = query.message.chat.username;
  const action = query.data;

  await bot.answerCallbackQuery(query.id);
  if (action === 'build') {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    await buildMenu(chatId, query.message.message_id);
  } else if (action.startsWith('project_')) {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    const projectKey = action.replace('project_', '');
    const project = projects[projectKey];
    const channelId = project.chatId;

    if (project) {
      if (project.isJSProject) {
        await buildJSProject(project, channelId, chatId, userName, false);
      } else {
        await buildPHPProject(project, channelId, chatId, userName, false);
      }
    }
  } else if (action === 'list_repo') {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    await listRepoMenu(chatId, query.message.message_id);
  } else if (action.startsWith('repo_')) {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    const repoKey = action.replace('repo_', '');
    const repo = listRepo.find(repo => repo.name === repoKey);
    if (repo) {
      await bot.sendMessage(chatId, `${repo.name} - ${repo.url}`);
    }
  } else if (action === 'active_iteration') {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    if (!await checkTokenExpired(bot, chatId)) {
      userStates[chatId] = { waitingForToken: true };
      await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:');
      return;
    }
    const listIteration = await getListIteration();
    if (listIteration && listIteration.length > 0) {
      await bot.sendMessage(chatId, `🗓️ Danh sách iteration:\n${listIteration.map(iteration => `- ${iteration?.projectTeam?.teamName || ''} / ${iteration.name} - *${iteration.status}* \`(${iteration.startDate} to ${iteration.endDate})\``).join('\n')}`, { parse_mode: 'Markdown' });
    } else {
      await bot.sendMessage(chatId, 'Không có iteration nào.');
    }
  } else if (action === 'need_approve_task') {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    if (!await checkTokenExpired(bot, chatId)) {
      userStates[chatId] = { waitingForToken: true };
      await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:');
      return;
    }
    await listNeedApprovalMemberMenu(bot, chatId, query.message.message_id);
  } else if (action.startsWith('list_approve_member_')) {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    if (!await checkTokenExpired(bot, chatId)) {
      userStates[chatId] = { waitingForToken: true };
      await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:');
      return;
    }
    const memberId = action.replace('list_approve_member_', '');
    if (memberId === 'all') {
      // await bot.sendMessage(chatId, '👤 Tất cả member:');
      // await bot.sendMessage(chatId, `👤 Thông tin member: ${Object.values(whitelistId).map(member => `- ${member.name}`).join('\n')}`);
    } else {
      const member = Object.values(whitelistId).find(member => member.id === parseInt(memberId));
      await bot.sendMessage(chatId, `🔋 Đang tải dữ liệu task cần phê duyệt cho *${member.name}*...`, { parse_mode: 'Markdown' });

      if (member) {
        const userIteration = await getNeedApprovalTaskByMemberUsername(member.username);
        if (userIteration && userIteration.length > 0) {
          await bot.sendMessage(chatId, `👤 Danh sách task cần phê duyệt của \`${member.name}\`:`, { parse_mode: 'Markdown' });
          for (const task of userIteration) {
            await bot.sendMessage(chatId, `- [${task.storyJiraCode || '0'}](https://nevel-tech.atlassian.net/browse/${task.storyJiraCode || '0'}) *${task.storyTitle}* \n\t\t\t+ [${task.issue?.jiraCode || '0'}](https://nevel-tech.atlassian.net/browse/${task.issue?.jiraCode || '0'}) ${task.issue?.title || ''}`);
          }
        } else {
          await bot.sendMessage(chatId, `👤 \`${member.name}\` không có task nào cần phê duyệt.`, { parse_mode: 'Markdown' });
        }
      } else {
        await bot.sendMessage(chatId, 'Không tìm thấy member.');
      }
    }
  } else if (action === 'need_estimate_task') {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    if (!await checkTokenExpired(bot, chatId)) {
      userStates[chatId] = { waitingForToken: true };
      await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:');
      return;
    }
    await listNeedEstimateMemberMenu(bot, chatId, query.message.message_id);
  } else if (action.startsWith('list_estimate_member_')) {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    if (!await checkTokenExpired(bot, chatId)) {
      userStates[chatId] = { waitingForToken: true };
      await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:');
      return;
    }
    const memberId = action.replace('list_estimate_member_', '');
    if (memberId === 'all') {
      // await bot.sendMessage(chatId, '👤 Tất cả member:');
      // await bot.sendMessage(chatId, `👤 Thông tin member: ${Object.values(whitelistId).map(member => `- ${member.name}`).join('\n')}`);
    } else {
      const member = Object.values(whitelistId).find(member => member.id === parseInt(memberId));
      await bot.sendMessage(chatId, `🔋 Đang tải dữ liệu task cần estimate cho *${member.name}*...`, { parse_mode: 'Markdown' });

      if (member) {
        const userIteration = await getNeedEstimateTaskByMemberUsername(member.username);
        if (userIteration && userIteration.length > 0) {
          await bot.sendMessage(chatId, `👤 Danh sách task cần estimate của \`${member.name}\`:`, { parse_mode: 'Markdown' });
          for (const task of userIteration) {
            await bot.sendMessage(chatId, `- [${task.storyJiraCode || '0'}](https://nevel-tech.atlassian.net/browse/${task.storyJiraCode || '0'}) *${task.storyTitle}* \n\t\t\t+ [${task.issue?.jiraCode || '0'}](https://nevel-tech.atlassian.net/browse/${task.issue?.jiraCode || '0'}) ${task.issue?.title || ''}`);
          }
        } else {
          await bot.sendMessage(chatId, `👤 \`${member.name}\` không có task nào cần estimate.`, { parse_mode: 'Markdown' });
        }
      } else {
        await bot.sendMessage(chatId, 'Không tìm thấy member.');
      }
    }
  } else if (action === 'list_member') {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    await bot.sendMessage(chatId, `🧑🏻‍💻 Danh sách member dev 3:\n${Object.values(whitelistId).map(member => `- [${member.name}](https://t.me/${member.username})`).join('\n')}`, { parse_mode: 'Markdown' });
  } else if (action === 'approval_task') {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    if (!await checkTokenExpired(bot, chatId)) {
      userStates[chatId] = { waitingForToken: true };
      await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:');
      return;
    }
    await listApprovalMemberMenu(bot, chatId, query.message.message_id);
  } else if (action.startsWith('approval_member_')) {
    if (!await checkWhiteList(chatId)) {
      return;
    }
    if (!await checkTokenExpired(bot, chatId)) {
      userStates[chatId] = { waitingForToken: true };
      await bot.sendMessage(chatId, 'Vui lòng gửi token mới của bạn:');
      return;
    }
    const memberId = action.replace('approval_member_', '');
    if (memberId === 'all') {
      await bot.sendMessage(chatId, `🔋 Đang tải dữ liệu để phê duyệt task cho tất cả member...`, { parse_mode: 'Markdown' });
      await approveTaskByAllMembers(bot, chatId);
    } else {
      const member = Object.values(whitelistId).find(member => member.id === parseInt(memberId));
      await bot.sendMessage(chatId, `🔋 Đang tải dữ liệu để phê duyệt task cho *${member.name}*...`, { parse_mode: 'Markdown' });

      if (member) {
        await approveTaskByMemberUsername(bot, chatId, member.username);
      } else {
        await bot.sendMessage(chatId, 'Không tìm thấy member.');
      }
    }
  }
});

bot.onText(/\/([a-zA-Z0-9_]+)/, async (msg) => {
  const chatId = msg.chat.id;
  const userName = msg.chat.username;
  const command = msg.text?.replace('/', '');
  const project = Object.values(projects).find(project => project.commandChat === command);

  if (!project) {
    return;
  }

  const channelId = project.chatId;

  if (project) {
    if (project.isJSProject) {
      await buildJSProject(project, channelId, chatId, userName, false);
    } else {
      await buildPHPProject(project, channelId, chatId, userName, false);
    }
  }
});

bot.on('channel_post', async (msg) => {
  try {
    if (msg?.chat?.type === 'channel') {
      const text = msg.text;
      const project = Object.values(projects).find(project => project.commandChat === text);
      if (project) {
        const channelId = project.chatId;
        if (project.isJSProject) {
          await buildJSProject(project, channelId, -1002513182802, '', true);
        } else {
          await buildPHPProject(project, channelId, -1002513182802, '', true);
        }
      }
    }
  } catch (error) {
    console.error('Error handling channel post:', error);
  }
});

// Xử lý tin nhắn để cập nhật token
bot.on('message', async (msg) => {
  const chatId = msg.chat.id;
  const text = msg.text;

  if (userStates[chatId]?.waitingForToken) {
    try {
      const dataDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir);
      }

      const cookiePath = path.join(dataDir, 'cookies.json');
      const cookieData = {
        cookie: text
      };
      fs.writeFileSync(cookiePath, JSON.stringify(cookieData, null, 2));

      delete userStates[chatId];

      await bot.sendMessage(chatId, '✅ Token đã được cập nhật thành công!');
    } catch (error) {
      console.error('Error updating token:', error);
      await bot.sendMessage(chatId, '❌ Có lỗi xảy ra khi cập nhật token. Vui lòng thử lại.');
    }
  }
});
