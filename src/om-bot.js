const { readFileSync } = require('fs');
const { join } = require('path');
const whitelistId = require('./config/whitelistid');

const escapeMarkdown = (text) => {
    if (!text) return '';
    return text
      .replace(/_/g, '\\_')
      .replace(/\*/g, '\\*')
      .replace(/\[/g, '\\[')
      .replace(/\]/g, '\\]')
      .replace(/\(/g, '\\(')
      .replace(/\)/g, '\\)')
      .replace(/~/g, '\\~')
      .replace(/`/g, '\\`')
      .replace(/>/g, '\\>')
      .replace(/#/g, '\\#')
      .replace(/\+/g, '\\+')
      .replace(/-/g, '\\-')
      .replace(/=/g, '\\=')
      .replace(/\|/g, '\\|')
      .replace(/{/g, '\\{')
      .replace(/}/g, '\\}')
      .replace(/\./g, '\\.')
      .replace(/:/g, '\\:')
      .replace(/,/g, '\\,')
      .replace(/</g, '\\<')
      .replace(/>/g, '\\>')
      .replace(/'/g, '\\\'')
      .replace(/&/g, '\\&')
      .replace(/"/g, '\\"')
      .replace(/$/g, '\\\'')
      .replace(/!/g, '\\!');
};

const getListIteration = async () => {
    try {
        const cookies = readFileSync(join(__dirname, 'data', 'cookies.json'), 'utf8');
        const cookie = JSON.parse(cookies);
        const response = await fetch(
            `${process.env.WEB_OM_API_URL}/api/iterations?status=active`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cookie': cookie.cookie
                }
            }
        );
        const data = await response.json();
        if (data && data.length > 0) {
            return data.filter(item => item.status.toLowerCase() === 'active');
        }
        return [];
    } catch (error) {
        console.error('Error fetching list iteration:', error);
        return [];
    }
}

const getListMember = (chatId) => {
    const listMember = Object.values(whitelistId);
    const member = listMember.find(member => member.id === chatId);
    if (member && member.isAdmin) {
        return listMember;
    }
    return listMember.filter(member => member.id === chatId);
}

const listApprovalMemberMenu = async (bot, chatId, messageId) => {
    let listMember = getListMember(chatId);
    const member = Object.values(whitelistId).find(member => member.id === chatId);
    if (member && member.isAdmin) {
        listMember = [
            {
                id: 'all',
                name: 'Tất cả',
            },
            ...listMember
        ];
    }
    const listMemberKeyboard = {
      reply_markup: {
        inline_keyboard: [
          ...listMember.map((member) => ([
            { text: member.name, callback_data: `approval_member_${member.id}` }
          ]))
        ]
      }
    };
    
    await bot.editMessageText(
      'Chọn member để phê duyệt task:',
      {
        chat_id: chatId,
        message_id: messageId,
        ...listMemberKeyboard
      }
    );
}

const listNeedApprovalMemberMenu = async (bot, chatId, messageId) => {
    console.log('chatId', chatId);
    const listMemberKeyboard = {
      reply_markup: {
        inline_keyboard: [
        //   [{ text: '📝 Tất cả', callback_data: 'list_approve_member_all' }],
          ...chunk(getListMember(chatId).map((member) => ({
            text: member.name,
            callback_data: `list_approve_member_${member.id}`
          })), 2)
        ]
      }
    };
    
    await bot.editMessageText(
      'Chọn member để xem task cần phê duyệt:',
      {
        chat_id: chatId,
        message_id: messageId,
        ...listMemberKeyboard
      }
    );
}

const listNeedEstimateMemberMenu = async (bot, chatId, messageId) => {
    const listMemberKeyboard = {
      reply_markup: {
        inline_keyboard: [
        //   [{ text: '📝 Tất cả', callback_data: 'list_approve_member_all' }],
          ...chunk(getListMember(chatId).map((member) => ({
            text: member.name,
            callback_data: `list_estimate_member_${member.id}`
          })), 2)
        ]
      }
    };
    
    console.log(listMemberKeyboard);
    await bot.editMessageText(
      'Chọn member để xem task cần estimate:',
      {
        chat_id: chatId,
        message_id: messageId,
        ...listMemberKeyboard
      }
    );
}

const getMembersByProjectId = async (projectId) => {
    try {
        const cookies = readFileSync(join(__dirname, 'data', 'cookies.json'), 'utf8');
        const cookie = JSON.parse(cookies);
        const response = await fetch(
            `${process.env.WEB_OM_API_URL}/api/project-teams/${projectId}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cookie': cookie.cookie
                }
            }
        );
        const data = await response.json();
        if (data && data.teamMembers) {
            return data.teamMembers;
        }
        return [];
    } catch (error) {
        console.error('Error fetching members by iteration:', error);
        return [];
    }
}

const getAllTaskByIterationAndMember = async (iterationId, memberId) => {
    try {
        const cookies = readFileSync(join(__dirname, 'data', 'cookies.json'), 'utf8');
        const cookie = JSON.parse(cookies);
        const response = await fetch(
            `${process.env.WEB_OM_API_URL}/api/iterations/${iterationId}/member-tasks?memberId=${memberId}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cookie': cookie.cookie
                }
            }
        );
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching task by iteration:', error);
        return [];
    }
}

const getNotApprovedTaskByIterationAndMember = async (iterationId, memberId) => {
    try {
        const data = await getAllTaskByIterationAndMember(iterationId, memberId);
        const tasks = [];
        if (data && data.length > 0) {
            for (const item of data) {
                if (item.tasks) {
                    const storyTasks = item.tasks?.map(it => ({
                        storyId: item?.story?.id || '',
                        storyTitle: item?.story?.title || '',
                        storyJiraCode: item?.story?.jiraCode || '',
                        ...it
                    }))?.filter(it => it.estimationStatus?.toUpperCase() === 'PROPOSED' && it.issue?.status?.toUpperCase() !== 'DONE') || [];
                    tasks.push(...storyTasks);
                }
            }
        }
        return tasks;
    } catch (error) {
        console.error('Error fetching task by iteration:', error);
        return [];
    }
}

const getNotEstimateTaskByIterationAndMember = async (iterationId, memberId) => {
    try {
        const data = await getAllTaskByIterationAndMember(iterationId, memberId);
        const tasks = [];
        if (data && data.length > 0) {
            for (const item of data) {
                if (item.tasks) {
                    const storyTasks = item.tasks.map(it => ({
                        storyId: item?.story?.id || '',
                        storyTitle: item?.story?.title || '',
                        storyJiraCode: item?.story?.jiraCode || '',
                        ...it
                    })).filter(it => it.needsEstimation && it.issue?.status?.toLowerCase() !== 'done');
                    tasks.push(...storyTasks);
                }
            }
        }
        return tasks;
    } catch (error) {
        console.error('Error fetching task by iteration:', error);
        return [];
    }
}

const getUserIteration = async (memberUsername) => {
    try {
        const userIteration = [];
        const listIteration = await getListIteration();
        if (listIteration && listIteration.length > 0) {
            for (const iteration of listIteration) {
                const members = await getMembersByProjectId(iteration.projectTeamId);
                if (members && members.length > 0) {
                    const member = members.find(item => item.username === memberUsername);
                    if (member) {
                        userIteration.push({
                            iterationId: iteration.id,
                            memberId: member.id
                        });
                    }
                }
            }
        }
        return userIteration;
    } catch (error) {
        console.error('Error fetching task by member:', error);
        return [];
    }
}
const getNeedApprovalTaskByMemberUsername = async (memberUsername) => {
    try {
        const userIteration = await getUserIteration(memberUsername);
        const userTask = [];
        if (userIteration && userIteration.length > 0) {
            for (const iteration of userIteration) {
                const tasks = await getNotApprovedTaskByIterationAndMember(iteration.iterationId, iteration.memberId);
                if (tasks && tasks.length > 0) {
                    userTask.push(...tasks);
                }
            }
        }
        return userTask;
    } catch (error) {
        console.error('Error fetching task by member:', error);
        return [];
    }
}
  

const getNeedEstimateTaskByMemberUsername = async (memberUsername) => {
    try {
        const userIteration = [];
        const userTask = [];
        const listIteration = await getListIteration();
        if (listIteration && listIteration.length > 0) {
            for (const iteration of listIteration) {
                const members = await getMembersByProjectId(iteration.projectTeamId);
                if (members && members.length > 0) {
                    const member = members.find(item => item.username === memberUsername);
                    if (member) {
                        userIteration.push({
                            iterationId: iteration.id,
                            memberId: member.id
                        });
                    }
                }
            }
        }
        if (userIteration && userIteration.length > 0) {
            for (const iteration of userIteration) {
                const tasks = await getNotEstimateTaskByIterationAndMember(iteration.iterationId, iteration.memberId);
                if (tasks && tasks.length > 0) {
                    userTask.push(...tasks);
                }
            }
        }
        return userTask;
    } catch (error) {
        console.error('Error fetching task by member:', error);
        return [];
    }
}

const checkTokenExpired = async (bot, chatId) => {
    try {
      const cookies = readFileSync(join(__dirname, 'data', 'cookies.json'), 'utf8');
      const cookie = JSON.parse(cookies);
      const response = await fetch(
          `${process.env.WEB_OM_API_URL}/api/iterations?status=active`,
          {
              method: 'GET',
              headers: {
                  'Content-Type': 'application/json',
                  'Cookie': cookie.cookie
              }
          }
      );
      const data = await response.json();
      if (response.status < 300 && data.code !== 'TOKEN_EXPIRED' && data.code !== 'INVALID_TOKEN') {
        return true;
      }
      await bot.sendMessage(chatId, 'Token đã hết hạn sử dụng. Vui lòng cập nhật lại token.');
      return false;
    } catch (error) {
      console.error('Error fetching list iteration:', error);
      await bot.sendMessage(chatId, error.message);
      return false;
    }
}

const getUsernameByChatId = (chatId) => {
    const users = Object.values(whitelistId);
    const user = users.find(user => user.id === chatId);
    return user?.username;
}

const sleep = async (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms));
}

const approveTaskByMemberUsername = async (bot, chatId, memberUsername) => {
    try {
        const userIteration = await getNeedApprovalTaskByMemberUsername(memberUsername);
        if (userIteration && userIteration.length > 0) {
            for (const task of userIteration) {
                const cookies = readFileSync(join(__dirname, 'data', 'cookies.json'), 'utf8');
                const cookie = JSON.parse(cookies);
                const username = getUsernameByChatId(chatId);
                if (!task.issue?.originalEstimate) {
                    await bot.sendMessage(chatId, `Task *${task.storyTitle}* cần estimate.`, { parse_mode: 'Markdown' });
                    continue;
                }
                // await bot.sendMessage(chatId, `Đang approve task: \n- [${task.storyJiraCode || '0'}](https://nevel-tech.atlassian.net/browse/${task.storyJiraCode || '0'}) *${task.storyTitle}* \n\t\t\t+ [${task.issue?.jiraCode || '0'}](https://nevel-tech.atlassian.net/browse/${task.issue?.jiraCode || '0'}) ${task.issue?.title || ''}`);
                // await bot.sendMessage(-4518541881, `@${username} Đang approve task: \n- [${task.storyJiraCode || '0'}](https://nevel-tech.atlassian.net/browse/${task.storyJiraCode || '0'}) *${task.storyTitle}* \n\t\t\t+ [${task.issue?.jiraCode || '0'}](https://nevel-tech.atlassian.net/browse/${task.issue?.jiraCode || '0'}) ${task.issue?.title || ''}`);
                const response = await fetch(`${process.env.WEB_OM_API_URL}/api/iterations/${task.iterationId}/issues/${task.issueId}/estimate/review`, {
                    method: 'PUT',
                    headers: {
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Cache-Control': 'no-cache',
                        'Connection': 'keep-alive',
                        'Content-Type': 'application/json',
                        'Pragma': 'no-cache',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"macOS"',
                        'Cookie': cookie.cookie
                    },
                    body: JSON.stringify({
                        status: "APPROVED",
                        comment: "Approved by bot"
                    })
                });
                await sleep(1000);
                // console.log(response);
                // const data = await response.json();
                // console.log(data);
            }
            await bot.sendMessage(chatId, `Approve task cho *${memberUsername}* thành công.`, { parse_mode: 'Markdown' });
            // await bot.sendMessage(-4518541881, `@${username} Approve task cho *${memberUsername}* thành công.`, { parse_mode: 'Markdown' });
        } else {
            await bot.sendMessage(chatId, `*${memberUsername}* không có task nào cần approve.`, { parse_mode: 'Markdown' });
            // await bot.sendMessage(-4518541881, `*${memberUsername}* không có task nào cần approve.`, { parse_mode: 'Markdown' });
        }
    } catch (error) {
        await bot.sendMessage(chatId, `Approve task cho *${memberUsername}* thất bại. ${error.message}`);
        // await bot.sendMessage(-4518541881, `@${username} Approve task cho *${memberUsername}* thất bại. ${error.message}`);
        console.error('Error fetching task by member:', error);
        await bot.sendMessage(chatId, error.message);
    }
}

const approveTaskByAllMembers = async (bot, chatId) => {
    try {
        const listMember = getListMember(chatId);
        for (const member of listMember) {
            await bot.sendMessage(chatId, `\`----- Bắt đầu cho *${member.name}*... -----\`\n`, { parse_mode: 'Markdown' });
            // await bot.sendMessage(-4518541881, `\`----- Bắt đầu cho *${member.name}*... -----\`\n`, { parse_mode: 'Markdown' });
            await approveTaskByMemberUsername(bot, chatId, member.username);
            await sleep(3000);
            await bot.sendMessage(chatId, `\`----- Kết thúc *${member.name}* -----\`\n`, { parse_mode: 'Markdown' });
            // await bot.sendMessage(-4518541881, `\`----- Kết thúc *${member.name}* -----\`\n`, { parse_mode: 'Markdown' });
        }
    } catch (error) {
        console.error('Error fetching task by member:', error);
        await bot.sendMessage(chatId, error.message);
    }
}

const chunk = (arr, size) => {
  return Array.from({ length: Math.ceil(arr.length / size) }, (v, i) =>
    arr.slice(i * size, i * size + size)
  );
};

module.exports = {
    approveTaskByAllMembers,
    getListIteration,
    listApprovalMemberMenu,
    listNeedApprovalMemberMenu,
    listNeedEstimateMemberMenu,
    getNeedApprovalTaskByMemberUsername,
    getNeedEstimateTaskByMemberUsername,
    checkTokenExpired,
    approveTaskByMemberUsername,
    escapeMarkdown
}