const GoogleChatBot = require('../src/google-chat-bot');

async function main() {
  // Khởi tạo bot
  const bot = new GoogleChatBot({
    spaceId: 'spaces/YOUR_SPACE_ID', // Thay thế bằng Space ID thực tế
    port: 3000
  });

  // Đăng ký handler xử lý tin nhắn
  bot.onMessage(async (message) => {
    console.log('📨 Received message:', message.text);
    
    const text = message.text.toLowerCase();
    
    // Xử lý các lệnh đặc biệt
    if (text.startsWith('/help')) {
      await bot.reply(message, `🤖 **Hướng dẫn sử dụng:**

• **/help** - Xem hướng dẫn này
• **/status** - Kiểm tra trạng thái bot
• **/time** - Xem thời gian hiện tại
• **/echo [text]** - Lặp lại tin nhắn
• **/card** - Gửi thẻ mẫu

Hãy thử các lệnh trên!`);
    }
    
    else if (text.startsWith('/status')) {
      await bot.reply(message, `✅ **Trạng thái <PERSON>:**
• 🟢 Online và hoạt động bình thường
• 📊 Đã xử lý: ${Date.now()} tin nhắn
• ⏰ Thời gian: ${new Date().toLocaleString('vi-VN')}`);
    }
    
    else if (text.startsWith('/time')) {
      const now = new Date();
      await bot.reply(message, `🕐 **Thời gian hiện tại:**
• 📅 Ngày: ${now.toLocaleDateString('vi-VN')}
• ⏰ Giờ: ${now.toLocaleTimeString('vi-VN')}
• 🌍 Múi giờ: ${now.toLocaleString('vi-VN', { timeZoneName: 'long' })}`);
    }
    
    else if (text.startsWith('/echo')) {
      const echoText = text.replace('/echo', '').trim();
      if (echoText) {
        await bot.reply(message, `🔊 **Echo:** ${echoText}`);
      } else {
        await bot.reply(message, '❌ Vui lòng nhập nội dung để echo. Ví dụ: /echo Xin chào!');
      }
    }
    
    else if (text.startsWith('/card')) {
      // Gửi card mẫu
      const cardData = {
        header: {
          title: '🎉 Thẻ mẫu',
          subtitle: 'Đây là một thẻ mẫu từ bot'
        },
        sections: [
          {
            widgets: [
              {
                textParagraph: {
                  text: 'Đây là nội dung của thẻ. Bạn có thể thêm nhiều thông tin ở đây.'
                }
              },
              {
                keyValue: {
                  topLabel: 'Trạng thái',
                  content: '✅ Hoạt động',
                  contentMultiline: true
                }
              },
              {
                buttons: [
                  {
                    textButton: {
                      text: '🔗 Xem thêm',
                      onClick: {
                        action: {
                          function: 'viewMore'
                        }
                      }
                    }
                  }
                ]
              }
            ]
          }
        ]
      };
      
      await bot.replyWithCard(message, cardData);
    }
    
    else {
      // Trả lời tin nhắn thông thường
      await bot.reply(message, `👋 Xin chào! Bạn đã gửi: "${message.text}"

💡 Gõ **/help** để xem các lệnh có sẵn.`);
    }
  });

  // Khởi động bot
  try {
    await bot.start();
    console.log('🎉 Bot đã khởi động thành công!');
    
    // Gửi tin nhắn chào mừng
    await bot.sendMessage(bot.spaceId, '🤖 Bot đã sẵn sàng! Gõ /help để xem hướng dẫn.');
    
  } catch (error) {
    console.error('❌ Lỗi khởi động bot:', error);
  }
}

// Xử lý tắt ứng dụng
process.on('SIGINT', () => {
  console.log('\n🛑 Đang tắt bot...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Đang tắt bot...');
  process.exit(0);
});

// Chạy ứng dụng
main().catch(console.error); 