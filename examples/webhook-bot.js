const { GoogleChatBot } = require('../src/google-chat-bot');

async function runWebhookBot() {
  console.log('🤖 Starting Webhook Google Chat Bot...\n');

  // Tạo bot instance với webhook support
  const bot = new GoogleChatBot({
    useServiceAccount: true,
    port: 3000,
    webhookPath: '/webhook'
  });

  try {
    // Khởi tạo bot
    await bot.initialize();
    console.log('✅ Bot initialized successfully\n');

    // Đăng ký handler để xử lý tin nhắn đến
    bot.onMessage(async (message, event) => {
      console.log('\n📨 Received message:');
      console.log('From:', message.sender?.displayName || 'Unknown');
      console.log('Text:', message.text);
      console.log('Space:', message.space?.displayName || 'Unknown');

      // Xử lý các lệnh khác nhau
      const text = message.text.toLowerCase();
      
      if (text.includes('hello') || text.includes('xin chào')) {
        await bot.reply(message, 'Xin chào! Tôi là bot Google Chat 🤖');
      }
      else if (text.includes('help') || text.includes('trợ giúp')) {
        const helpCard = bot.createSimpleCard(
          'Trợ giúp Bot',
          'Các lệnh có sẵn',
          '• hello/xin chào: Chào hỏi\n• help/trợ giúp: Hiển thị trợ giúp\n• time: Hiển thị thời gian hiện tại\n• weather: Thông tin thời tiết\n• joke: Kể chuyện cười',
          [
            bot.createButton('Xem thêm', 'moreHelp', {}),
            bot.createButton('Đóng', 'close', {})
          ]
        );
        await bot.sendCard(message.space.name, helpCard);
      }
      else if (text.includes('time') || text.includes('thời gian')) {
        const now = new Date();
        const timeString = now.toLocaleString('vi-VN');
        await bot.reply(message, `🕐 Thời gian hiện tại: ${timeString}`);
      }
      else if (text.includes('weather') || text.includes('thời tiết')) {
        const weatherCard = bot.createSimpleCard(
          'Thông tin thời tiết',
          'Hà Nội, Việt Nam',
          '🌤️ Nhiệt độ: 25°C\n💧 Độ ẩm: 70%\n🌬️ Gió: 10 km/h\n☔ Mưa: 20%',
          [
            bot.createButton('Cập nhật', 'refreshWeather', {}),
            bot.createButton('Xem chi tiết', 'weatherDetails', {})
          ]
        );
        await bot.sendCard(message.space.name, weatherCard);
      }
      else if (text.includes('joke') || text.includes('cười')) {
        const jokes = [
          'Tại sao lập trình viên thích mùa thu? Vì có nhiều Fall! 😄',
          'Một lập trình viên đi vào bar. Order 1 beer, 0 beers, 999999999 beers, -1 beer, "qwerty" beers... 😂',
          'Tại sao bot không bao giờ buồn? Vì nó luôn có backup! 🤖',
          'Lập trình viên: "Code của tôi hoạt động, tôi không biết tại sao!" 😅'
        ];
        const randomJoke = jokes[Math.floor(Math.random() * jokes.length)];
        await bot.reply(message, randomJoke);
      }
      else {
        // Trả lời mặc định
        await bot.reply(message, 'Tôi không hiểu lệnh này. Gõ "help" để xem danh sách lệnh có sẵn.');
      }
    });

    // Bắt đầu webhook server
    await bot.startWebhookServer();
    console.log('\n🚀 Webhook server is running!');
    console.log('📡 Webhook URL: http://localhost:3000/webhook');
    console.log('🏥 Health check: http://localhost:3000/health');
    console.log('\n💡 Để test bot:');
    console.log('1. Deploy lên server có public URL (ngrok, Heroku, etc.)');
    console.log('2. Cấu hình webhook URL trong Google Chat');
    console.log('3. Gửi tin nhắn trong space để test');
    console.log('\n⏹️  Press Ctrl+C to stop the server');

    // Giữ server chạy
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down...');
      await bot.stopWebhookServer();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Error running webhook bot:', error);
  }
}

// Chạy demo
if (require.main === module) {
  runWebhookBot().catch(console.error);
}

module.exports = { runWebhookBot }; 