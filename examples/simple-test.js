const GoogleChat = require('../src/google-chat');

async function testGoogleChat() {
  console.log('🧪 Testing Google Chat connection...');
  
  const chat = new GoogleChat();
  
  try {
    // Khởi tạo kết nối
    const initialized = await chat.initialize();
    
    if (!initialized) {
      console.error('❌ Failed to initialize Google Chat');
      return;
    }
    
    console.log('✅ Google Chat initialized successfully');
    
    // Gửi tin nhắn test
    const spaceId = process.env.GOOGLE_CHAT_SPACE_ID || 'spaces/YOUR_SPACE_ID';
    
    if (spaceId === 'spaces/YOUR_SPACE_ID') {
      console.log('⚠️  Please set GOOGLE_CHAT_SPACE_ID environment variable');
      console.log('   Example: export GOOGLE_CHAT_SPACE_ID=spaces/AAQAqu0wnEU');
      return;
    }
    
    console.log(`📤 Sending test message to ${spaceId}...`);
    
    const result = await chat.sendMessage(spaceId, `🧪 Test message from bot at ${new Date().toLocaleString('vi-VN')}
    
✅ Bot is working correctly!`);
    
    console.log('✅ Test message sent successfully!');
    console.log('📋 Message ID:', result.name);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('Service account key file not found')) {
      console.log('\n💡 Solution:');
      console.log('1. Download your service account JSON file from Google Cloud Console');
      console.log('2. Place it in src/data/service-account-key.json');
      console.log('3. Make sure the service account has Chat API permissions');
    }
    
    if (error.message.includes('PERMISSION_DENIED')) {
      console.log('\n💡 Solution:');
      console.log('1. Enable Google Chat API in Google Cloud Console');
      console.log('2. Add service account email to your Google Chat space');
      console.log('3. Grant appropriate permissions to the service account');
    }
  }
}

// Chạy test
testGoogleChat(); 