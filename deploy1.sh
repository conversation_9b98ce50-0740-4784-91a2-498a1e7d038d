#!/bin/bash
CHAT_ID=$2

# Function to get GitHub URL from list-repo.js
get_github_url() {
    local project_name="$1"
    # Use node to get the URL from list-repo.js
    node -e "
        try {
            const listRepo = require('/Users/<USER>/Documents/augment-projects/building-bot/src/config/list-repo.js');
            const repo = listRepo.find(r => r.name === '$project_name');
            if (repo) {
                console.log(repo.url);
            } else {
                console.log('');
            }
        } catch (error) {
            console.error('Error:', error.message);
            console.log('');
        }
    "
}

# Function to send Telegram message
send_telegram_message() {
    local message="$1"
    # Replace \n with %0A for Telegram API
    message=$(echo "$message" | sed 's/\\n/%0A/g')
    curl -s "https://api.telegram.org/bot7889785330:AAEEP-ECFFq2nUAB2ItEUprmw8l0LB5x-Qo/sendMessage" \
        -d "chat_id=$CHAT_ID" \
        -d "text=${message}" \
        -d "parse_mode=HTML"
}

# Function to truncate message
truncate_message() {
    local message="$1"
    local max_length=1000  # Leave room for other message parts
    if [ ${#message} -gt $max_length ]; then
        echo "${message:0:$max_length}... (truncated)"
    else
        echo "$message"
    fi
}

# Function to extract PR ID and add link
process_pr_link() {
    local message="$1"
    local project_name="$2"
    # Get GitHub URL for the project
    local github_url=$(get_github_url "$project_name")
    echo "Debug: GitHub URL for $project_name is $github_url" >&2
    
    if [ -z "$github_url" ]; then
        echo "$message"
        return
    fi
    
    # Extract PR ID using regex (assuming format like #123 or PR #123)
    if [[ $message =~ (?:PR\s*#|#)([0-9]+) ]]; then
        local pr_id="${BASH_REMATCH[1]}"
        echo "Debug: Found PR ID: $pr_id" >&2
        # Replace PR ID with link
        local processed_message=$(echo "$message" | sed -E "s/(PR\s*#|#)${pr_id}/<a href=\"${github_url}\/pull\/${pr_id}\">\1${pr_id}<\/a>/g")
        echo "$processed_message"
    else
        echo "$message"
    fi
}

# Function to extract JIRA ID and add link
process_jira_link() {
    local message="$1"
    # Extract JIRA ID using regex (format like Z123, ZABC123, Z123ABC456)
    if [[ $message =~ (Z[A-Z0-9\-]*[0-9]) ]]; then
        local jira_id="${BASH_REMATCH[1]}"
        echo "Debug: Found JIRA ID: $jira_id" >&2
        # Replace JIRA ID with link
        local processed_message=$(echo "$message" | sed -E "s/(Z[A-Z0-9\-]*[0-9])/<a href=\"https:\/\/nevel-tech.atlassian.net\/browse\/\1\">\1<\/a>/g")
        echo "$processed_message"
    else
        echo "$message"
    fi
}

# Get commit information and deploy
ssh zt "cd /usr/share/nginx/s2-brand/$1 && \
    git fetch origin dev-deploy && \
   echo 'COMMIT_HASH:' \$(git rev-parse origin/dev-deploy) && \
    echo 'COMMIT_MESSAGE_START' && \
    git log -1 --pretty=%B origin/dev-deploy && \
    echo 'COMMIT_MESSAGE_END' && \
    echo 'COMMIT_AUTHOR:' \$(git log -1 --pretty=%an origin/dev-deploy) && \
    git pull origin dev-deploy > /dev/null && \
    echo 'DEPLOY_SUCCESS'" | while read -r line; do
    if [[ $line == DEPLOY_SUCCESS ]]; then
        # Process PR link and JIRA link
        PROCESSED_MESSAGE=$(process_pr_link "$COMMIT_MESSAGE" "$1")
        PROCESSED_MESSAGE=$(process_jira_link "$PROCESSED_MESSAGE")
        TRUNCATED_MESSAGE=$(truncate_message "$PROCESSED_MESSAGE")
        # Send Telegram notification with commit information
        send_telegram_message "🚀 <b>Deployment Successful</b>%0A<i>Project:</i> $1%0A<i>Commit:</i> $COMMIT_HASH%0A<i>Message:</i> $TRUNCATED_MESSAGE%0A<i>Author:</i> $COMMIT_AUTHOR"
    elif [[ $line =~ ^COMMIT_HASH:(.*)$ ]]; then
        COMMIT_HASH="${BASH_REMATCH[1]}"
    elif [[ $line == COMMIT_MESSAGE_START ]]; then
        COMMIT_MESSAGE=""
    elif [[ $line == COMMIT_MESSAGE_END ]]; then
        : # Do nothing, message is complete
    elif [[ $line =~ ^COMMIT_AUTHOR:(.*)$ ]]; then
        COMMIT_AUTHOR="${BASH_REMATCH[1]}"
    elif [[ -n $line ]]; then
        if [[ -n $COMMIT_MESSAGE ]]; then
            COMMIT_MESSAGE="${COMMIT_MESSAGE}%0A${line}"
        else
            COMMIT_MESSAGE="${line}"
        fi
    fi
done